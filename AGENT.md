# 🚀 Project: 2D Graphical Android Game

## 📝 Project Description

This project is a 2D graphical game for Android, developed using Kotlin and leveraging Jetpack libraries for a modern, robust, and scalable application. The game features distinct screens for Gameplay, Help, and About. Crucially, the game state information is persisted using Protobuf DataStore, ensuring reliable saving and loading of player progress. This project also supports multiplayer gameplay and utilizes Google Firebase for backend services, with monetization managed through Google Ads and product flavors for free and paid versions.

## ✨ Core Features

- **Game Screen**: The primary interface for interactive 2D gameplay.
- **Stats Screen**: Displays the top scores for the game for solo players and multiplayer.
- **Help Screen**: Provides instructions and tips for playing the game.
- **Settings Screen**: Provides primary interface for user to view and change game options.
- **About Screen**: Displays information about the game, its developers, and version.
- **Game State Persistence**: Uses Protobuf DataStore to save and restore game progress, player scores, and settings.
- **Multiplayer**: Supports two simultaneous players, each on their own device, for a competitive experience.
- **Free/Paid Versions**: The app is released in two distinct flavors with different features enabled or disabled.

## 🛠️ Technology Stack

- **Language**: Kotlin
- **Platform**: Android
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM (Model-View-ViewModel)
- **State Management**: Jetpack Compose's observable state, Kotlin Coroutines, and Flow.
- **Data Persistence**: Protobuf DataStore for local data.
- **Navigation**: Jetpack Navigation Compose.
- **Dependency Injection**: Hilt.
- **Backend as a Service**: Google Firebase
- **Monetization**: Google Mobile Ads SDK
- **Build System**: Gradle (using product flavors for free/paid versions).

## 🏛️ Architectural Design

The application will follow a clean, modular MVVM architecture broken down into distinct layers, with the addition of dedicated services for Firebase and Ads integration.

- **UI Layer (Composeables)**: Responsible for rendering the user interface and handling user input.
- **ViewModel Layer**: Contains UI-related logic, manages state, and interacts with repositories.
- **Data Layer (Repositories & Data Sources)**: Provides a clean API for the ViewModel. This includes local data from Protobuf DataStore and remote data from Firebase.
- **Firebase & Ad Services**: A dedicated layer to abstract the complexities of Firebase API calls and Google Ad management from the rest of the application.

## 🎮 Game Specifics

- **2D Graphics**: The game will utilize a 2D drawing surface, likely managed within a Compose Canvas.
- **Game Logic**: Game rules, collision detection, and entity movements will be encapsulated in separate classes.

## 🌐 Networking & Multiplayer

This project requires a robust multiplayer component. The game will support real-time, two-player matches. Player actions and game state synchronization will be handled through a backend service, likely Firebase Firestore or Realtime Database, to ensure a smooth and consistent experience for both players.

## ☁️ Backend Services: Google Firebase

Google Firebase will serve as the core Backend as a Service (BaaS) for this project. The following Firebase services will be implemented:

- **Firebase Authentication**: For user registration and login, essential for distinguishing between players.
- **Firestore/Realtime Database**: To facilitate multiplayer session management and serve as the backend for the global leaderboard.
- **Firebase Cloud Messaging (FCM)**: To send push notifications to users.
- **Firebase Analytics**: To gather data on user behavior and game performance.
- **Crashlytics**: To provide detailed crash reports for debugging and maintenance.

## 💰 Monetization & Build Variants

The app's monetization strategy and feature set will be managed through Gradle product flavors.

- **free Flavor**: This version will be ad-supported and will have certain features disabled. It will integrate Google Mobile Ads to display banner and interstitial ads.
- **paid Flavor**: This version will be ad-free and will have all features unlocked.

The code base will use build configuration and feature flags to dynamically turn features and ads on/off based on the active build flavor. Ad behavior (interstitial only, banner only, or both) will be configurable.

## 🤖 AI Agent Role

As the AI Agent, your primary role in this project will be to assist in:

- **Code Generation & Architecture**: Generating Kotlin code for Compose UI, ViewModels, and Repositories while ensuring the new backend and ad components are integrated into the existing MVVM architecture seamlessly.
- **Firebase & Network Logic**: Implementing the complex logic for Firebase services, including user authentication, real-time database synchronization for multiplayer, and handling potential network failures.
- **Product Flavor & Ads Integration**: Configuring Gradle product flavors, and implementing the Google Mobile Ads SDK with the necessary logic to conditionally show ads based on the build variant and user settings.
- **Debugging & Testing**: Identifying and suggesting fixes for bugs related to all new components, and assisting in writing tests to ensure the robustness of the multiplayer, ad, and flavor-based logic.
- **Documentation**: Generating KDoc comments for new functions and classes, and updating this AGENT.md file as needed.

## 🎯 Key Focus Areas for the AI Agent

- **Protobuf DataStore & Firebase Integration**: Ensure smooth data flow between local persistence and the cloud-based backend.
- **Multiplayer Synchronization**: Pay close attention to the real-time synchronization logic to prevent desynchronization issues between players.
- **Configurable Ads & Flavors**: Correctly manage the logic for enabling/disabling ads and other features based on the Gradle build flavor.
- **Dependency Injection (Hilt)**: Properly configure Hilt to inject all new dependencies, including Firebase and Ad services.

## Common Screen Layout
Each screen has the same layout:
- A Title Bar at the top with a Screen Title on the left and Title Bar Options on the right.
- A Navigation Bar at the bottom with buttons that allow the user to switch to the Game, Settings, Help, Leaderboard, and About screens. None of the buttons are active when the Loading screen is displayed and all of the buttons except the button for the current screen are active when any other screen is displayed.
- A Work Area that is centered vertically between the Title and Navigation Bars and centered horizontally between the sides of the screen.
- A subtle background picture (to be described later) that fills the entire Work Area. The background is the same for all screens.
- The Game screen is not scrollable while the other screens can be scrolled if needed.

## Additional Game Detail

- At the beginning of a game the Game Screen displays a two-dimensional matrix of squares arranged in rows and columns.
- Each square is randomly colored one of fives colors (Red, Green, Blue, Yellow, and Black).
- Every pair of adjacent squares of the same color are a part of a cluster.
- A cluster extends to all squares adjacent to a member of the cluster and of the same color.
- Clicking on a square in a cluster causes all of the squares in the cluster to be removed from the game screen.
- Clicking on a square not in a cluster does nothing.
- When a cluster is removed from the game screen, the created hole is filled by the surrounding squares following an algorithm to be described later.

## Cluster Hole Filling Algorithm

- The square clicked in a cluster defines the location of a "gravity well".
- The gravity well divides the board into four hemispheres: top, bottom, left, and right.
- After all of the squares in the cluster are removed from the display, the remaining squares visually move toward the gravity well.
- Squares move only in straight horizontal and vertical directions.
- The filling process is a loop that alternates between Vertical and Horizontal Phases, starting with a Vertical Phase. 
- The loop continues until there is both no more vertical and horizontal movement.  
- A Vertical Phase consists of all the squares in the top hemisphere moving down first and then all the squares in the bottom hemisphere moving up.
- Squares moving down go as far as they can until they either reach the gravity well row or are blocked by another square. 
- Squares moving up go as far as they can until they either reach the gravity well row or are blocked by another square. 
- A Horizontal Phase consists of all squares in the left hemisphere moving right first and then all the squares in the right hemisphere moving left. 
- Squares moving right go as far as they can until they either reach the gravity well column or are blocked by another square.
- Squares moving left go as far as they can until they either reach the gravity well column or are blocked by another square.

## Experimental Hole Filling Algorithm
NOTE: This experimental algorithm is not currently used.
In this version of the game, squares of the same color stick together and can't be separated from the cluster they are a part of. 
A square in a cluster can move toward the gravity well, but if any further movement would separate the square from the cluster, then the square drags along other squares in the cluster to maintain the cluster integrity. 
The dragged squares may also drag other squares as needed to maintain the cluster integrity. 
The experimental hole filling algorithm is the same as the standard algorithm except that the when squares are being moved clusters can't be broken up by the movement. 
In addition, if a square that is not part of any cluster is moved to a location where it now has one or more sides adjacent to a one or more squares of the same color, then 
1) If the other squares are also not a part of any cluster, then the moved square and the other squares form a new cluster. 
2) If at least one of the other squares is part of a cluster, then the moved square and the other squares join that cluster.


## Device Orientation
The game does not visually rotate the screen when device orientation changes. 
Instead, only the vertical and horizontal directions rotate with the device. 
For example, by rotating the screen 90 degrees clockwise, the user can change squares that used to be to the left of a square to now be above the square.
Thus, the orientation of the screen determines how squares will move to fill a hole.
The app looks at the device orientation at the time a gravity well is defined and uses that orientation to determine the directions of movement.
Reorienting the device while a hole is being filled does not change the directions of movement.

## Animation
**Cluster Removal**
- A cluster is removed from the board by all of the squares in the cluster shrinking to nothing.

**Cluster Hole Filling**
- All squares move at the same uniform rate.
- During the Vertical Phase, the squares being moved up and the squares being moved down move simultaneously. 
- During the Horizontal Phase, the squares being moved left and the squares being moved right move simultaneously.
- During the Completion Phase, the squares being moved left all move simultaneously. 

## Game Completion
The app automatically ends the game when there are no more clusters to remove. 
A "Game Over!" message is displayed for a short period of time. 
Tapping a completed game board starts a new solo game.

## Solo Game Scoring

The solo game scoring algorithm is a combination of points earned from each move and a bonus for completing the game.
1. Base Score (Per Move):
The points for each move are calculated based on the size of the region cleared. 
To incentivize clearing larger regions, we'll use the 4th power function: Sbase=(number of squares in region)**4. 

2. Time Bonus (Per Move):
To encourage faster play, a time bonus is added for each move. The bonus is inversely proportional to the time taken to make that move, measured from the end of the previous move.
Stime_bonus=(1000/milliseconds elapsed between moves) * Sbase
The total score for each move is the sum of the base score and the time bonus:
Smove=Sbase+Stime_bonus.

3. Completion Bonus:
When a player clears the entire screen, they receive a large bonus. This rewards the player for successfully completing the game by strategically removing all regions.
Scompletion_bonus=Stotal**2 where Stotal is the total of all Smove scores.

4. All-Color Region Bonuses:
These bonuses are cumulative and reward players for clearing all squares of a single color in a single move. 
The bonus increases with each subsequent all-color region cleared. 
The rewards are designed to encourage a player to wait for a large-scale region.
Let's assume the five colors are C1, C2, C3, C4, and C5. 
The number of squares of each color at the start of the game is NC1,NC2,…,NC5. 
If a player clears a region of a single color with a size equal to one of these initial counts, they receive a bonus.
- 1st all-color region cleared: Sbonus1=Stotal**2
- 2nd all-color region cleared: Sbonus2=Stotal**3
- 3rd all-color region cleared: Sbonus3=Stotal**4
- 4th all-color region cleared: Sbonus4=Stotal**5
- 5th all-color region cleared: Sbonus5=Stotal**6

5. Final Score Solo Game:
Sfinal = Stotal + Scompletion_bonus + Sbonus1 + Sbonus2 + Sbonus3 + Sbonus4 + Sbonus5

## Two-Player Game Scoring
The two-player scoring incorporates the solo game features while adding competitive bonuses and penalties. 
Both players start with the same board and the same number of moves available. 

1. Primary Score:
Each player's primary score is calculated using the solo game formula. 
2. First Finisher Bonus:
The first player to come to the end of their game (no more moves available) receives a bonus of twice their primary score. 
S1st_finish_bonus=Sfinal * 2
3. Second Player Penalty
Without any warning, the second player has 5 seconds to complete their game after the first player has finished. 
If the second player does not complete their game within this time, the game automatically ends and their final score is half of their primary score. 
