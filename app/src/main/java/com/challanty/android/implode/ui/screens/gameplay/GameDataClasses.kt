package com.challanty.android.implode.ui.screens.gameplay

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import java.util.UUID

/**
 * Represents the visual state of a square during animations.
 */
enum class SquareVisualState {
    Normal,     // Square is in its normal state
    Shrinking,  // Square is shrinking (being removed)
    Sliding,    // Square is sliding to a new position
    Gone        // Square has been removed
}

/**
 * Represents the different colors a square can have.
 */
enum class SquareColor(val colorVal: Color) {
    Red(Color(0xFFE57373)),
    Green(Color(0xFF81C784)),
    Blue(Color(0xFF64B5F6)),
    Yellow(Color(0xFFFFB74D)),
    Black(Color(0xFF424242));

    companion object {
        fun random(): SquareColor {
            return values().random()
        }
    }
}

/**
 * Represents the state of a single square on the game board.
 */
data class SquareState(
    val color: SquareColor? = null,
    val row: Int = 0,
    val col: Int = 0,
    val targetRow: Int = 0,
    val targetCol: Int = 0,
    val visualState: SquareVisualState = SquareVisualState.Normal,
    val targetScale: Float = 1f,
    val targetAlpha: Float = 1f,
    val targetOffsetX: Dp = 0.dp,
    val targetOffsetY: Dp = 0.dp,
    val offsetX: Dp = 0.dp,
    val offsetY: Dp = 0.dp,
    val id: String = UUID.randomUUID().toString()
) {
    /**
     * Creates a copy with normalized visual state based on color presence.
     */
    fun withNormalizedVisualState(): SquareState {
        val hasColor = color != null
        return copy(
            visualState = if (hasColor) SquareVisualState.Normal else SquareVisualState.Gone,
            targetScale = if (hasColor) 1f else 0f,
            targetAlpha = if (hasColor) 1f else 0f
        )
    }
}

/**
 * Type alias for the game board to improve readability.
 */
typealias GameBoard = List<List<SquareState>>

/**
 * Represents the different animation states in the game.
 * This state machine replaces simple boolean flags for better control flow.
 */
sealed class AnimationState {
    /**
     * No animation is currently running. Ready for user input.
     */
    object Idle : AnimationState()

    /**
     * Cluster squares are shrinking to nothing.
     */
    data class ClusterRemoval(
        val clusterSquares: Set<Pair<Int, Int>>,
        val gravityWell: Pair<Int, Int>
    ) : AnimationState()

    /**
     * Remaining squares are moving toward the gravity well to fill holes.
     */
    data class HoleFilling(
        val gravityWell: Pair<Int, Int>
    ) : AnimationState()

    /**
     * Final cleanup and game completion check.
     */
    object Completing : AnimationState()

    /**
     * Game over state - no valid moves remaining.
     */
    object GameOver : AnimationState()

    // Helper properties
    val isAnimating: Boolean get() = this !is Idle && this !is GameOver
    val canAcceptInput: Boolean get() = this is Idle
}

/**
 * Represents the complete state of the game.
 * This immutable data class centralizes all game state management.
 */
data class GameState(
    val board: GameBoard = emptyList(),
    val score: Int = 0,
    val animationState: AnimationState = AnimationState.Idle,
    val lastMoveTime: Long = System.currentTimeMillis(),
    val allColorRegionsCleared: Int = 0,
    val initialColorCounts: Map<SquareColor, Int> = emptyMap(),
    val boardRows: Int = 10,
    val boardCols: Int = 8,
    val scoreAnimation: ScoreAnimation? = null,
    val totalMoveScore: Int = 0  // Sum of all move scores (Stotal) for new scoring formula
) {
    // State modification helpers
    fun withScore(newScore: Int): GameState = copy(score = newScore)
    fun withBoard(newBoard: GameBoard): GameState = copy(board = newBoard)
    fun withAnimationState(state: AnimationState): GameState = copy(animationState = state)
    fun withLastMoveTime(time: Long): GameState = copy(lastMoveTime = time)
    fun withAllColorRegionsCleared(count: Int): GameState = copy(allColorRegionsCleared = count)
    fun withInitialColorCounts(counts: Map<SquareColor, Int>): GameState = copy(initialColorCounts = counts)
    fun withBoardDimensions(rows: Int, cols: Int): GameState = copy(boardRows = rows, boardCols = cols)
    fun withScoreAnimation(animation: ScoreAnimation?): GameState = copy(scoreAnimation = animation)
    fun withTotalMoveScore(total: Int): GameState = copy(totalMoveScore = total)

    // Convenience methods for score updates
    fun addScore(points: Int): GameState = copy(score = score + points)
    fun updateLastMoveTime(): GameState = copy(lastMoveTime = System.currentTimeMillis())

    // Animation state convenience methods
    fun toIdle(): GameState = copy(animationState = AnimationState.Idle)
    fun toClusterRemoval(clusterSquares: Set<Pair<Int, Int>>, gravityWell: Pair<Int, Int>): GameState =
        copy(animationState = AnimationState.ClusterRemoval(clusterSquares, gravityWell))
    fun toHoleFilling(gravityWell: Pair<Int, Int>): GameState =
        copy(animationState = AnimationState.HoleFilling(gravityWell))
    fun toCompleting(): GameState = copy(animationState = AnimationState.Completing)
    fun toGameOver(): GameState = copy(animationState = AnimationState.GameOver)

    // Game state queries
    val isAnimating: Boolean get() = animationState.isAnimating
    val canAcceptInput: Boolean get() = animationState.canAcceptInput

    fun isGameComplete(): Boolean {
        return board.all { row -> row.all { square -> square.color == null } }
    }

    fun hasValidMoves(): Boolean {
        for (row in board.indices) {
            for (col in board[row].indices) {
                val square = board[row][col]
                if (square.color != null) {
                    // Check if this square is part of a cluster of size >= 2
                    val cluster = findClusterAt(row, col, square.color!!)
                    if (cluster.size >= 2) return true
                }
            }
        }
        return false
    }

    private fun findClusterAt(startRow: Int, startCol: Int, targetColor: SquareColor): Set<Pair<Int, Int>> {
        val queue = ArrayDeque<Pair<Int, Int>>()
        val visited = mutableSetOf<Pair<Int, Int>>()
        val cluster = mutableSetOf<Pair<Int, Int>>()

        queue.addLast(Pair(startRow, startCol))

        while (queue.isNotEmpty()) {
            val (row, col) = queue.removeFirst()

            if (visited.contains(Pair(row, col))) continue
            visited.add(Pair(row, col))

            if (row < 0 || row >= board.size || col < 0 || col >= board[0].size) continue

            val square = board[row][col]
            if (square.color != targetColor) continue

            cluster.add(Pair(row, col))

            // Add adjacent squares to queue
            queue.addLast(Pair(row - 1, col)) // Up
            queue.addLast(Pair(row + 1, col)) // Down
            queue.addLast(Pair(row, col - 1)) // Left
            queue.addLast(Pair(row, col + 1)) // Right
        }

        return cluster
    }
}

/**
 * Represents a score animation with position and content information.
 */
data class ScoreAnimation(
    val totalPoints: Int,
    val baseScore: Int,
    val timeBonus: Int,
    val allColorBonus: Int,
    val allColorBonusText: String,
    val gravityWellRow: Int,
    val gravityWellCol: Int,
    val isVisible: Boolean = true
)

/**
 * Board utility functions for working with immutable collections.
 */
object BoardUtils {
    /**
     * Creates an immutable copy of a board.
     */
    fun toImmutableBoard(board: List<List<SquareState>>): GameBoard {
        return board.map { row -> row.toList() }
    }

    /**
     * Creates a mutable copy of a board for animation purposes.
     */
    fun toMutableBoard(board: GameBoard): MutableList<MutableList<SquareState>> {
        return board.map { row -> row.map { it.copy() }.toMutableList() }.toMutableList()
    }

    /**
     * Generates a new random board with the specified dimensions.
     */
    fun generateRandomBoard(rows: Int, cols: Int): GameBoard {
        return List(rows) { r ->
            List(cols) { c ->
                SquareState(
                    color = SquareColor.random(),
                    row = r,
                    col = c,
                    targetRow = r,
                    targetCol = c
                )
            }
        }
    }

    /**
     * Generates a test board with nested rectangle outlines for debugging.
     * Each rectangle outline is a different color, forming distinct clusters.
     */
    fun generateTestBoard(rows: Int, cols: Int): GameBoard {
        // Create empty board first
        val board = Array(rows) { r ->
            Array(cols) { c ->
                SquareState(
                    color = null,
                    row = r,
                    col = c,
                    targetRow = r,
                    targetCol = c
                )
            }
        }

        val colors = SquareColor.values()
        var colorIndex = 0

        // Draw nested rectangles from outside to inside
        val maxRectangles = minOf(rows / 2, cols / 2, colors.size)

        for (rectIndex in 0 until maxRectangles) {
            val color = colors[colorIndex % colors.size]
            colorIndex++

            val top = rectIndex
            val bottom = rows - 1 - rectIndex
            val left = rectIndex
            val right = cols - 1 - rectIndex

            // Skip if rectangle is too small
            if (top >= bottom || left >= right) break

            // Draw top and bottom edges
            for (c in left..right) {
                if (board[top][c].color == null) {
                    board[top][c] = board[top][c].copy(color = color)
                }
                if (top != bottom && board[bottom][c].color == null) {
                    board[bottom][c] = board[bottom][c].copy(color = color)
                }
            }

            // Draw left and right edges
            for (r in top..bottom) {
                if (board[r][left].color == null) {
                    board[r][left] = board[r][left].copy(color = color)
                }
                if (left != right && board[r][right].color == null) {
                    board[r][right] = board[r][right].copy(color = color)
                }
            }
        }

        // Fill any remaining empty squares with a neutral color
        for (r in 0 until rows) {
            for (c in 0 until cols) {
                if (board[r][c].color == null) {
                    board[r][c] = board[r][c].copy(color = SquareColor.Yellow)
                }
            }
        }

        return board.map { row -> row.toList() }
    }

    /**
     * Calculates initial color counts for a new board.
     */
    fun calculateInitialColorCounts(board: GameBoard): Map<SquareColor, Int> {
        return board.flatten()
            .mapNotNull { it.color }
            .groupingBy { it }
            .eachCount()
    }
}
