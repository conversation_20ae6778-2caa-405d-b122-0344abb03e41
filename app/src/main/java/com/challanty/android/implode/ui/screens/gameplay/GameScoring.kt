package com.challanty.android.implode.ui.screens.gameplay

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles all game scoring logic following AGENT.md specifications.
 *
 * Solo Game Scoring Formula:
 * 1. Base Score (Per Move): Sbase = (number of squares in region)^4
 * 2. Time Bonus (Per Move): Stime_bonus = (1000/milliseconds elapsed between moves) * Sbase
 * 3. Move Score: Smove = Sbase + Stime_bonus
 * 4. Completion Bonus: Scompletion_bonus = Stotal^2 where Stotal is sum of all move scores
 * 5. All-Color Region Bonuses:
 *    - 1st all-color region: Sbonus1 = Stotal^2
 *    - 2nd all-color region: Sbonus2 = Stotal^3
 *    - 3rd all-color region: Sbonus3 = Stotal^4
 *    - 4th all-color region: Sbonus4 = Stotal^5
 *    - 5th all-color region: Sbonus5 = Stotal^6
 * 6. Final Score: Sfinal = Stotal + Scompletion_bonus + Sbonus1 + Sbonus2 + Sbonus3 + Sbonus4 + Sbonus5
 *
 * This class is stateless and thread-safe.
 */
@Singleton
class GameScoring @Inject constructor() {
    
    companion object {
        // Constants for new scoring formula
        private const val TIME_BONUS_BASE = 1000
    }
    
    /**
     * Represents the current scoring state.
     * This is a lightweight data class for passing scoring information.
     */
    data class ScoringState(
        val currentScore: Int = 0,
        val lastMoveTime: Long = System.currentTimeMillis(),
        val allColorRegionsCleared: Int = 0,
        val initialColorCounts: Map<SquareColor, Int> = emptyMap(),
        val totalMoveScore: Int = 0  // Sum of all move scores (Stotal)
    ) {
        fun withScore(newScore: Int): ScoringState = copy(currentScore = newScore)
        fun withLastMoveTime(time: Long): ScoringState = copy(lastMoveTime = time)
        fun withAllColorRegionsCleared(count: Int): ScoringState = copy(allColorRegionsCleared = count)
        fun withInitialColorCounts(counts: Map<SquareColor, Int>): ScoringState = copy(initialColorCounts = counts)
        fun withTotalMoveScore(total: Int): ScoringState = copy(totalMoveScore = total)
    }
    
    /**
     * Represents the result of a scoring calculation.
     */
    data class ScoreResult(
        val baseScore: Int,
        val timeBonus: Int,
        val allColorBonus: Int,
        val allColorBonusText: String,
        val totalScore: Int,
        val moveScore: Int = baseScore + timeBonus  // Smove = Sbase + Stime_bonus
    ) {
        val hasTimeBonus: Boolean get() = timeBonus > 0
        val hasAllColorBonus: Boolean get() = allColorBonus > 0
    }
    
    /**
     * Calculates the score for a move and returns both the result and updated state.
     * This is the main entry point for scoring calculations.
     */
    fun calculateMoveScore(
        clusterSize: Int,
        clusterColor: SquareColor,
        currentBoard: GameBoard,
        scoringState: ScoringState
    ): Pair<ScoreResult, ScoringState> {
        val currentTime = System.currentTimeMillis()

        // Calculate base score: Sbase = (number of squares in region)^4
        val baseScore = calculateBaseScore(clusterSize)

        // Calculate time bonus: Stime_bonus = (1000/milliseconds elapsed) * Sbase
        val timeBonus = calculateTimeBonus(currentTime, scoringState.lastMoveTime, baseScore)

        // Calculate move score: Smove = Sbase + Stime_bonus
        val moveScore = baseScore + timeBonus

        // Update total move score (Stotal)
        val newTotalMoveScore = scoringState.totalMoveScore + moveScore

        // Check for all-color bonus (calculated based on current Stotal)
        val (allColorBonus, updatedState) = checkAllColorBonus(
            clusterSize,
            clusterColor,
            currentBoard,
            scoringState.withTotalMoveScore(newTotalMoveScore)
        )
        val allColorBonusText = if (allColorBonus > 0) {
            "All ${clusterColor.name} cleared! +$allColorBonus"
        } else ""

        // Total score includes move score and any all-color bonus
        val totalScore = moveScore + allColorBonus

        val scoreResult = ScoreResult(
            baseScore = baseScore,
            timeBonus = timeBonus,
            allColorBonus = allColorBonus,
            allColorBonusText = allColorBonusText,
            totalScore = totalScore,
            moveScore = moveScore
        )

        val finalState = updatedState.withLastMoveTime(currentTime)

        println("Score calculation: base=$baseScore, time=$timeBonus, move=$moveScore, allColor=$allColorBonus, total=$totalScore")

        return Pair(scoreResult, finalState)
    }
    
    /**
     * Calculates base score: Sbase = (number of squares in region)^4
     */
    private fun calculateBaseScore(clusterSize: Int): Int {
        return clusterSize * clusterSize * clusterSize * clusterSize
    }

    /**
     * Calculates time bonus: Stime_bonus = (1000/milliseconds elapsed between moves) * Sbase
     */
    private fun calculateTimeBonus(currentTime: Long, lastMoveTime: Long, baseScore: Int): Int {
        val millisecondsElapsed = currentTime - lastMoveTime
        if (millisecondsElapsed <= 0) return 0

        val timeBonusMultiplier = TIME_BONUS_BASE.toDouble() / millisecondsElapsed.toDouble()
        return (timeBonusMultiplier * baseScore).toInt()
    }
    
    /**
     * Checks for all-color bonus based on new formula:
     * - 1st all-color region: Sbonus1 = Stotal^2
     * - 2nd all-color region: Sbonus2 = Stotal^3
     * - 3rd all-color region: Sbonus3 = Stotal^4
     * - 4th all-color region: Sbonus4 = Stotal^5
     * - 5th all-color region: Sbonus5 = Stotal^6
     *
     * The bonus is only awarded when the cluster being cleared contains ALL remaining squares
     * of that color that were present on the starting board.
     */
    fun checkAllColorBonus(
        clusterSize: Int,
        clusterColor: SquareColor,
        currentBoard: GameBoard,
        scoringState: ScoringState
    ): Pair<Int, ScoringState> {
        val initialCount = scoringState.initialColorCounts[clusterColor] ?: 0

        // Count how many squares of this color are currently on the board
        val currentCount = currentBoard.flatten().count { it.color == clusterColor }

        // Check if this cluster clears ALL remaining squares of this color AND
        // the cluster size equals the original count from the starting board
        if (clusterSize == currentCount && clusterSize == initialCount) {
            val newAllColorRegionsCleared = scoringState.allColorRegionsCleared + 1
            val stotal = scoringState.totalMoveScore

            // Calculate bonus: Stotal^(n+1) where n is the number of all-color regions cleared
            val bonus = calculateAllColorBonus(stotal, newAllColorRegionsCleared)

            println("All-color bonus! Cleared all $clusterColor squares ($clusterSize/$initialCount from start). Bonus: $bonus (Stotal=$stotal, region #$newAllColorRegionsCleared)")

            val updatedState = scoringState.withAllColorRegionsCleared(newAllColorRegionsCleared)
            return Pair(bonus, updatedState)
        }

        return Pair(0, scoringState)
    }

    /**
     * Calculates all-color bonus: Stotal^(n+1) where n is the number of all-color regions cleared
     */
    private fun calculateAllColorBonus(stotal: Int, regionNumber: Int): Int {
        if (stotal <= 0) return 0

        val power = regionNumber + 1 // 1st region = power 2, 2nd region = power 3, etc.
        var result = 1
        repeat(power) {
            result *= stotal
        }
        return result
    }
    
    /**
     * Calculates completion bonus for finishing the game.
     * Formula: Scompletion_bonus = Stotal^2 where Stotal is sum of all move scores
     */
    fun calculateCompletionBonus(totalMoveScore: Int): ScoreResult {
        val completionBonus = if (totalMoveScore > 0) {
            totalMoveScore * totalMoveScore  // Stotal^2
        } else {
            0
        }

        return ScoreResult(
            baseScore = 0,
            timeBonus = 0,
            allColorBonus = 0,
            allColorBonusText = if (completionBonus > 0) "Game Complete! +$completionBonus" else "",
            totalScore = completionBonus,
            moveScore = 0
        )
    }
}
