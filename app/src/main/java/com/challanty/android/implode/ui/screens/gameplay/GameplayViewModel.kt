package com.challanty.android.implode.ui.screens.gameplay

import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class GameplayViewModel @Inject constructor(
    private val gameLogic: GameLogic,
    private val animationController: GameAnimationController,
    private val gameScoring: GameScoring,
    private val orientationManager: DeviceOrientationManager,
    private val statsRepository: com.challanty.android.implode.data.repository.StatsRepository
) : ViewModel() {

    // Centralized game state
    private val _gameState = MutableStateFlow(GameState())
    val gameState: StateFlow<GameState> = _gameState.asStateFlow()

    // Convenience accessors for UI
    val gameBoard: StateFlow<GameBoard> = _gameState.map { it.board }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = emptyList()
    )

    val currentScore: StateFlow<Int> = _gameState.map { it.score }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = 0
    )

    val scoreAnimation: StateFlow<ScoreAnimation?> = _gameState.map { it.scoreAnimation }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = null
    )

    val animationState: StateFlow<AnimationState> = _gameState.map { it.animationState }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = AnimationState.Idle
    )

    init {
        initializeBoard()
    }

    fun initializeOrientationManager(context: android.content.Context) {
        orientationManager.initialize(context)
        orientationManager.startListening()
    }

    fun cleanupOrientationManager() {
        orientationManager.stopListening()
    }

    fun startNewGame() {
        val currentState = _gameState.value
        initializeBoard(currentState.boardRows, currentState.boardCols)
    }

    fun updateBoardSize(newSize: IntSize) {
        val currentState = _gameState.value
        if (currentState.animationState !is AnimationState.Idle) return

        val (rows, cols) = gameLogic.calculateOptimalBoardSize(
            workAreaWidth = newSize.width,
            workAreaHeight = newSize.height
        )

        if (rows != currentState.boardRows || cols != currentState.boardCols) {
            initializeBoard(rows, cols)
        }
    }

    private fun initializeBoard(rows: Int = 10, cols: Int = 8) {
        val newBoard = BoardUtils.generateRandomBoard(rows, cols)
        val initialColorCounts = BoardUtils.calculateInitialColorCounts(newBoard)

        _gameState.value = GameState(
            board = newBoard,
            score = 0,
            animationState = AnimationState.Idle,
            lastMoveTime = System.currentTimeMillis(),
            allColorRegionsCleared = 0,
            initialColorCounts = initialColorCounts,
            boardRows = rows,
            boardCols = cols,
            scoreAnimation = null,
            totalMoveScore = 0
        )
    }

    fun onSquareClicked(clickedRow: Int, clickedCol: Int) {
        val currentState = _gameState.value
        if (!currentState.canAcceptInput) return

        val clickedSquare = currentState.board[clickedRow][clickedCol]
        val currentColor = clickedSquare.color ?: return

        val cluster = gameLogic.findCluster(currentState.board, clickedRow, clickedCol, currentColor)
        if (cluster.size < 2) return

        val gravityWell = Pair(clickedRow, clickedCol)

        // Capture device orientation at the time gravity well is created (per AGENT.md spec)
        val orientationAtGravityWell = orientationManager.getCurrentOrientation()

        // Start cluster removal animation
        _gameState.value = currentState.toClusterRemoval(cluster, gravityWell)

        viewModelScope.launch {
            // Create scoring state from current game state
            val scoringState = GameScoring.ScoringState(
                currentScore = currentState.score,
                lastMoveTime = currentState.lastMoveTime,
                allColorRegionsCleared = currentState.allColorRegionsCleared,
                initialColorCounts = currentState.initialColorCounts,
                totalMoveScore = currentState.totalMoveScore
            )

            // Calculate and add score for this move (animation disabled)
            val (scoreResult, newScoringState) = gameScoring.calculateMoveScore(cluster.size, currentColor, currentState.board, scoringState)

            // Update state with new score and scoring data
            val newScore = currentState.score + scoreResult.totalScore
            val newTotalMoveScore = currentState.totalMoveScore + scoreResult.moveScore
            _gameState.value = _gameState.value
                .addScore(scoreResult.totalScore)
                .withLastMoveTime(newScoringState.lastMoveTime)
                .withAllColorRegionsCleared(newScoringState.allColorRegionsCleared)
                .withTotalMoveScore(newTotalMoveScore)

            // Update last game score in stats (for real-time tracking)
            viewModelScope.launch {
                statsRepository.updateLastGameScore(newScore)
            }

            // Animate cluster removal
            val mutableBoard = BoardUtils.toMutableBoard(currentState.board)
            animationController.animateClusterRemoval(
                clusterSquares = cluster,
                gravityWellLocation = gravityWell,
                board = mutableBoard,
                boardRows = currentState.boardRows,
                boardCols = currentState.boardCols,
                onBoardUpdate = { updatedBoard ->
                    _gameState.value = _gameState.value.withBoard(updatedBoard)
                }
            )

            // Start hole filling animation
            _gameState.value = _gameState.value.toHoleFilling(gravityWell)

            animationController.animateHoleFilling(
                gravityWell = gravityWell,
                board = mutableBoard,
                boardRows = currentState.boardRows,
                boardCols = currentState.boardCols,
                orientation = orientationAtGravityWell,
                onBoardUpdate = { updatedBoard ->
                    _gameState.value = _gameState.value.withBoard(updatedBoard)
                }
            )

            // Complete the move
            _gameState.value = _gameState.value.toCompleting()

            // Check for game completion
            val finalBoard = BoardUtils.toImmutableBoard(mutableBoard)
            val updatedState = _gameState.value.withBoard(finalBoard)

            if (updatedState.isGameComplete() || !updatedState.hasValidMoves()) {
                // Game is over - calculate completion bonus
                val completionBonus = gameScoring.calculateCompletionBonus(updatedState.totalMoveScore)
                val finalScore = updatedState.score + completionBonus.totalScore

                // Update state with completion bonus
                _gameState.value = updatedState
                    .addScore(completionBonus.totalScore)
                    .toGameOver()

                // Update stats with final score
                viewModelScope.launch {
                    statsRepository.updateGameScore(finalScore)
                }
            } else {
                _gameState.value = updatedState.toIdle()
            }
        }
    }

    fun onScoreAnimationComplete() {
        _gameState.value = _gameState.value.withScoreAnimation(null)
    }

    override fun onCleared() {
        super.onCleared()
        orientationManager.stopListening()
    }
}
